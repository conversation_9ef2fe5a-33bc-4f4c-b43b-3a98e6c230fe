<template>
  <CommonPage show-footer>
    <template #action>
      <NSpace>
        <NButton @click="goBack">
          <TheIcon icon="material-symbols:arrow-back" :size="18" class="mr-5"/>
          返回
        </NButton>
        <NButton type="primary" @click="handleAddCases">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5"/>
          添加测试用例
        </NButton>
      </NSpace>
    </template>

    <!-- 左右布局 -->
    <NGrid :cols="24" :x-gap="16">
      <!-- 左侧：基本信息 -->
      <NGridItem :span="5">
        <NCard title="基本信息" class="detail-card">
          <div class="detail-info">
            <div class="detail-item">
              <span class="detail-label">计划名称:</span>
              <span class="detail-value">{{ planDetail.plan_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">等级:</span>
              <NTag v-if="planDetail.level" :type="getLevelType(planDetail.level)" size="small">
                {{ getLevelText(planDetail.level) }}
              </NTag>
              <span v-else class="detail-value">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <NTag v-if="planDetail.status" :type="getStatusType(planDetail.status)" size="small">
                {{ getStatusText(planDetail.status) }}
              </NTag>
              <span v-else class="detail-value">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通过率:</span>
              <span class="detail-value" :style="getPassRateStyle(planDetail.pass_rate)">
                {{ formatPassRate(planDetail.pass_rate) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建人:</span>
              <span class="detail-value">{{ planDetail.creator_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间:</span>
              <span class="detail-value">{{ formatDate(planDetail.created_at) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">更新时间:</span>
              <span class="detail-value">{{ formatDate(planDetail.updated_at) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">描述:</span>
              <div class="detail-description">
                {{ planDetail.description || '-' }}
              </div>
            </div>
          </div>
        </NCard>
      </NGridItem>

      <!-- 右侧：关联的测试用例 -->
      <NGridItem :span="19">
        <NCard title="关联测试用例" class="detail-card">
          <template #header-extra>
            <NSpace>
              <NButton
                type="tertiary"
                size="small"
                @click="handleOpenConfig"
              >
                <TheIcon icon="material-symbols:settings" :size="16" class="mr-2"/>
                配置
              </NButton>
              <NButton size="small" @click="handleRefreshCases">
                <TheIcon icon="material-symbols:refresh" :size="16" class="mr-2"/>
                刷新
              </NButton>
              <NPopconfirm @positive-click="handleBatchDelete">
                <template #trigger>
                  <NButton
                    size="small"
                    type="error"
                    :disabled="selectedPlanCaseIds.length === 0"
                    :loading="batchDeleting"
                  >
                    <TheIcon icon="material-symbols:delete" :size="16" class="mr-2"/>
                    批量删除
                  </NButton>
                </template>
                确认删除选中的 {{ selectedPlanCaseIds.length }} 个测试用例？
              </NPopconfirm>
            </NSpace>
          </template>

          <NDataTable
              ref="caseTableRef"
              v-model:checked-row-keys="selectedPlanCaseIds"
              :columns="caseColumns"
              :data="planCases"
              :loading="casesLoading"
              :pagination="casesPagination"
              :scroll-x="1400"
              :row-key="(row) => row.id"
              size="small"
              :max-height="600"
          />
        </NCard>
      </NGridItem>
    </NGrid>
  </CommonPage>

  <!-- 添加测试用例弹窗 -->
  <NModal v-model:show="addCasesModalVisible" preset="dialog" title="添加测试用例" style="width: 900px;">
    <!-- 搜索栏 -->
    <div style="margin-bottom: 16px;">
      <NSpace>
        <NInput
            v-model:value="searchCaseName"
            placeholder="请输入用例名称搜索"
            clearable
            style="width: 300px;"
            @keypress.enter="handleSearchCases"
            @clear="handleClearSearch"
        />
        <NButton type="primary" @click="handleSearchCases">搜索</NButton>
        <NButton @click="handleClearSearch">清空</NButton>
      </NSpace>
    </div>

    <!-- 用例列表 -->
    <div v-if="!availableTestCases || availableTestCases.length === 0" style="text-align: center; padding: 40px;">
      <p style="color: #999;">{{ searchCaseName ? '没有找到匹配的测试用例' : '当前项目没有可添加的测试用例' }}</p>
      <p style="color: #ccc; font-size: 12px;">
        {{ searchCaseName ? '请尝试其他搜索条件' : '请确保项目中有状态为"已审核"且未添加到当前计划的测试用例' }}</p>
    </div>
    <NDataTable
        v-else
        ref="availableCasesTableRef"
        v-model:checked-row-keys="selectedCaseIds"
        :columns="availableCasesColumns"
        :data="availableTestCases"
        :loading="availableCasesLoading"
        :pagination="availableCasesPagination"
        :row-key="(row) => row.id"
        size="small"
        :scroll-x="800"
        :max-height="400"
    />
    <template #action>
      <NSpace>
        <NButton @click="addCasesModalVisible = false">取消</NButton>
        <NButton type="primary" @click="handleConfirmAddCases" :loading="addingCases">确定</NButton>
      </NSpace>
    </template>
  </NModal>

  <!-- 测试计划配置弹窗 -->
  <TestPlanConfigModal
    v-model:visible="configModalVisible"
    :plan-id="planId"
    plan-type="functional"
    :plan-name="planDetail.plan_name"
    @saved="handleConfigSaved"
  />
</template>

<script setup>
import {h, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {NButton, NInput, NPopconfirm, NSpace, NTag, NSelect, NPopover, useMessage} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import TestPlanConfigModal from '@/components/test-plan-config/TestPlanConfigModal.vue'
import {formatDate} from '@/utils'
import functionalTestPlanApi from '@/api/functionalTestPlan'
import projectApi from '@/api/project'

defineOptions({name: '功能测试计划详情'})

const route = useRoute()
const router = useRouter()
const $message = useMessage()

// 基本状态
const loading = ref(true)
const planId = ref(parseInt(route.params.id))
const planDetail = ref({})

// 测试用例相关状态
const casesLoading = ref(false)
const planCases = ref([])

// 添加用例弹窗相关状态
const addCasesModalVisible = ref(false)
const availableTestCases = ref([])
const availableCasesLoading = ref(false)
const selectedCaseIds = ref([])
const addingCases = ref(false)
const searchCaseName = ref('')

// 配置弹窗相关状态
const configModalVisible = ref(false)

// 关联用例列表分页配置
const casesPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    casesPagination.value.page = page
  },
  onUpdatePageSize: (pageSize) => {
    casesPagination.value.pageSize = pageSize
    casesPagination.value.page = 1
  }
})

// 可用用例分页配置
const availableCasesPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    availableCasesPagination.value.page = page
    loadAvailableTestCases()
  },
  onUpdatePageSize: (pageSize) => {
    availableCasesPagination.value.pageSize = pageSize
    availableCasesPagination.value.page = 1
    loadAvailableTestCases()
  }
})

// 批量删除相关状态
const selectedPlanCaseIds = ref([])
const batchDeleting = ref(false)

// 获取测试计划详情
const getPlanDetail = async () => {
  try {
    loading.value = true
    const {data} = await functionalTestPlanApi.getFunctionalTestPlan({plan_id: planId.value})
    planDetail.value = data
  } catch (error) {
    $message.error('获取测试计划详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取测试计划关联的用例
const getPlanCases = async () => {
  try {
    casesLoading.value = true
    const response = await functionalTestPlanApi.getTestPlanCases({plan_id: planId.value})
    planCases.value = response.data || []
    casesPagination.value.itemCount = planCases.value.length
    console.log('获取关联用例成功:', planCases.value.length, '个用例')
  } catch (error) {
    console.error('获取测试用例失败:', error)
    $message.error('获取测试用例失败')
    planCases.value = []
    casesPagination.value.itemCount = 0
  } finally {
    casesLoading.value = false
  }
}

// 加载可用的测试用例
const loadAvailableTestCases = async () => {
  if (!planDetail.value.project_id) return

  try {
    availableCasesLoading.value = true
    const params = {
      project_id: planDetail.value.project_id,
      plan_id: planId.value
    }

    // 如果有搜索条件，添加到参数中
    if (searchCaseName.value.trim()) {
      params.case_name = searchCaseName.value.trim()
    }

    const response = await functionalTestPlanApi.getApprovedTestCases(params)
    availableTestCases.value = response.data || []
    availableCasesPagination.value.itemCount = availableTestCases.value.length
  } catch (error) {
    console.error('加载可用测试用例失败:', error)
    $message.error('加载可用测试用例失败')
  } finally {
    availableCasesLoading.value = false
  }
}

// 搜索用例
const handleSearchCases = () => {
  loadAvailableTestCases()
}

// 清空搜索
const handleClearSearch = () => {
  searchCaseName.value = ''
  loadAvailableTestCases()
}

// 状态和等级渲染函数
const getStatusType = (status) => {
  const statusMap = {
    'not_started': 'default',
    'in_progress': 'warning',
    'completed': 'success'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status) => {
  const statusMap = {
    'not_started': '未开始',
    'in_progress': '进行中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

const getLevelType = (level) => {
  const levelMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'error'
  }
  return levelMap[level] || 'default'
}

const getLevelText = (level) => {
  const levelMap = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return levelMap[level] || level
}

// 执行状态相关函数
const getExecutionStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'passed': 'success',
    'failed': 'error',
    'blocked': 'info',
    'skipped': 'default'
  }
  return statusMap[status] || 'default'
}

const getExecutionStatusText = (status) => {
  const statusMap = {
    'pending': '待重试',
    'passed': '通过',
    'failed': '失败',
    'blocked': '阻塞',
    'skipped': '跳过'
  }
  return statusMap[status] || status
}

const executionStatusOptions = [

  {label: '通过', value: 'passed'},
  {label: '失败', value: 'failed'},
  {label: '阻塞', value: 'blocked'},
  {label: '跳过', value: 'skipped'},
  {label: '待重试', value: 'pending'}
]

// 关联用例表格列配置
const caseColumns = [
  {type: 'selection'},
  {title: '顺序', key: 'execution_order', width: 40},
  {title: '编号', key: 'case_number', width: 50},
  {title: '用例名称', key: 'case_name', width: 80, ellipsis: {tooltip: true}},
  {title: '用例步骤', key: 'test_steps', width: 120, ellipsis: {tooltip: true}},
  {title: '预期结果', key: 'expected_result', width: 120, ellipsis: {tooltip: true}},
  {
    title: '等级',
    key: 'case_level',
    width: 40,
    render: (row) => h(NTag, {type: getLevelType(row.case_level)}, {default: () => getLevelText(row.case_level)})
  },
  {
    title: '冒烟',
    key: 'is_smoke',
    width: 40,
    render: (row) => h(NTag, {type: row.is_smoke ? 'success' : 'default'}, {default: () => row.is_smoke ? '是' : '否'})
  },
  {
    title: '执行状态',
    key: 'execution_status',
    width: 80,
    render: (row) => h(NPopover, {
      trigger: 'click',
      placement: 'bottom'
    }, {
      trigger: () => h(NTag, {
        type: getExecutionStatusType(row.execution_status || 'passed'),
        size: 'medium',
        style: 'cursor: pointer; user-select: none;',
        bordered: false
      }, {
        default: () => getExecutionStatusText(row.execution_status || 'passed')
      }),
      default: () => h('div', {
        style: 'padding: 8px;'
      }, [
        h('div', {
          style: 'margin-bottom: 8px; font-weight: 500; color: #666;'
        }, '选择执行状态:'),
        h(NSpace, {
          vertical: true,
          size: 'small'
        }, {
          default: () => executionStatusOptions.map(option =>
            h(NTag, {
              type: getExecutionStatusType(option.value),
              size: 'small',
              style: 'cursor: pointer; width: 80px; text-align: center;',
              bordered: row.execution_status === option.value,
              onClick: () => handleUpdateCaseStatus(row.id, option.value)
            }, {
              default: () => option.label
            })
          )
        })
      ])
    })
  },
  {title: '创建人', key: 'creator_name', width: 100},
  {
    title: '操作',
    key: 'actions',
    width: 50,
    align: 'center',
    fixed: 'right',
    render(row, index) {
      return h(NSpace, {size: 'small'}, {
        default: () => [
          h(NButton, {
            size: 'tiny',
            type: 'primary',
            secondary: true,
            disabled: index === 0,
            onClick: () => moveCase(index, 'up')
          }, {default: () => '↑'}),
          h(NButton, {
            size: 'tiny',
            type: 'primary',
            secondary: true,
            disabled: index === planCases.value.length - 1,
            onClick: () => moveCase(index, 'down')
          }, {default: () => '↓'}),
          // h(NPopconfirm, {
          //   onPositiveClick: () => handleRemoveCase(row.id)
          // }, {
          //   default: () => '确认移除此用例？',
          //   trigger: () => h(NButton, {
          //     size: 'small',
          //     type: 'error',
          //     secondary: true
          //   }, {default: () => '移除'})
          // })
        ]
      })
    }
  }
]

// 可用测试用例表格列配置
const availableCasesColumns = [
  {type: 'selection'},
  {title: '用例编号', key: 'case_number', width: 120},
  {title: '用例名称', key: 'case_name', width: 200, ellipsis: {tooltip: true}},
  {title: '用例步骤', key: 'test_steps', width: 200, ellipsis: {tooltip: true}},
  {title: '预期结果', key: 'expected_result', width: 200, ellipsis: {tooltip: true}},
  {
    title: '用例等级',
    key: 'case_level',
    width: 100,
    render: (row) => h(NTag, {type: getLevelType(row.case_level)}, {default: () => getLevelText(row.case_level)})
  },
  {
    title: '是否冒烟',
    key: 'is_smoke',
    width: 100,
    render: (row) => h(NTag, {type: row.is_smoke ? 'success' : 'default'}, {default: () => row.is_smoke ? '是' : '否'})
  },
  {title: '创建人', key: 'creator_name', width: 100}
]

// 事件处理函数
const goBack = () => {
  router.back()
}

const handleRefreshCases = () => {
  getPlanCases()
}

// 打开配置弹窗
const handleOpenConfig = () => {
  configModalVisible.value = true
}

// 配置保存成功回调
const handleConfigSaved = () => {
  $message.success('配置已更新')
}

const handleAddCases = async () => {
  addCasesModalVisible.value = true
  selectedCaseIds.value = []
  await loadAvailableTestCases()
}

const handleConfirmAddCases = async () => {
  if (selectedCaseIds.value.length === 0) {
    $message.warning('请选择要添加的测试用例')
    return
  }

  try {
    addingCases.value = true
    await functionalTestPlanApi.addTestCasesToPlan({
      plan_id: planId.value,
      case_ids: selectedCaseIds.value
    })
    $message.success('测试用例添加成功')
    addCasesModalVisible.value = false
    selectedCaseIds.value = []
    // 刷新关联用例列表
    await getPlanCases()
  } catch (error) {
    console.error('添加测试用例失败:', error)
    $message.error('添加测试用例失败')
  } finally {
    addingCases.value = false
  }
}

// 批量删除关联用例
const handleBatchDelete = async () => {
  if (selectedPlanCaseIds.value.length === 0) {
    $message.warning('请选择要删除的测试用例')
    return
  }

  try {
    batchDeleting.value = true
    await functionalTestPlanApi.removeTestCasesFromPlan({
      plan_id: planId.value,
      case_ids: selectedPlanCaseIds.value
    })
    $message.success('测试用例批量删除成功')
    selectedPlanCaseIds.value = []
    // 刷新关联用例列表
    await getPlanCases()
    
    // 重新设置执行顺序
    const updatedCases = planCases.value.map((item, index) => ({
      ...item,
      execution_order: index + 1
    }))
    
    // 调用后端接口更新顺序
    await functionalTestPlanApi.updateCaseOrder({
      plan_id: planId.value,
      case_orders: updatedCases.map(item => ({
        case_id: item.id,
        execution_order: item.execution_order
      }))
    })
    
    // 再次刷新列表以显示更新后的顺序
    await getPlanCases()
    
    $message.success('执行顺序已自动更新')
  } catch (error) {
    console.error('批量删除测试用例失败:', error)
    $message.error('批量删除测试用例失败')
  } finally {
    batchDeleting.value = false
  }
}

const handleRemoveCase = async (caseId) => {
  try {
    await functionalTestPlanApi.removeTestCasesFromPlan({
      plan_id: planId.value,
      case_ids: [caseId]
    })
    $message.success('测试用例移除成功')
    // 刷新关联用例列表
    await getPlanCases()
  } catch (error) {
    console.error('移除测试用例失败:', error)
    $message.error('移除测试用例失败')
  }
}

// 移动用例顺序
const moveCase = async (currentIndex, direction) => {
  const cases = [...planCases.value]
  const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1

  if (targetIndex < 0 || targetIndex >= cases.length) {
    return
  }

  // 交换位置
  [cases[currentIndex], cases[targetIndex]] = [cases[targetIndex], cases[currentIndex]]

  // 更新执行顺序
  cases.forEach((item, index) => {
    item.execution_order = index + 1
  })

  try {
    // 更新本地数据
    planCases.value = cases

    // 调用后端接口更新顺序
    await functionalTestPlanApi.updateCaseOrder({
      plan_id: planId.value,
      case_orders: cases.map(item => ({
        case_id: item.id,
        execution_order: item.execution_order
      }))
    })

    $message.success('顺序调整成功')
  } catch (error) {
    console.error('更新顺序失败:', error)
    $message.error('顺序调整失败')
    // 失败时重新获取数据
    await getPlanCases()
  }
}

// 更新用例执行状态
const handleUpdateCaseStatus = async (caseId, status) => {
  try {
    await functionalTestPlanApi.updateCaseStatus({
      plan_id: planId.value,
      case_id: caseId,
      execution_status: status
    })

    // 更新本地数据
    const caseIndex = planCases.value.findIndex(c => c.id === caseId)
    if (caseIndex !== -1) {
      planCases.value[caseIndex].execution_status = status
    }

    // 重新获取计划详情以更新通过率
    await getPlanDetail()

    $message.success('执行状态更新成功')
  } catch (error) {
    console.error('更新执行状态失败:', error)
    $message.error('更新执行状态失败')
    // 失败时重新获取数据
    await getPlanCases()
  }
}

// 格式化通过率显示
const formatPassRate = (passRate) => {
  if (passRate === null || passRate === undefined) {
    return '暂无数据'
  }
  return `${parseFloat(passRate)}%`
}

// 获取通过率样式
const getPassRateStyle = (passRate) => {
  if (passRate === null || passRate === undefined) {
    return { color: '#999' }
  }

  const rate = parseFloat(passRate)
  let color = '#999'
  if (rate >= 80) {
    color = '#52c41a' // 绿色
  } else if (rate >= 60) {
    color = '#faad14' // 橙色
  } else {
    color = '#ff4d4f' // 红色
  }

  return { color, fontWeight: '500' }
}

// 初始化
onMounted(async () => {
  await getPlanDetail()
  await getPlanCases()
})
</script>

<style scoped>
.detail-card {
  height: 100%;
}

.detail-info {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.5;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 100px;
  margin-right: 8px;
  flex-shrink: 0;
}

.detail-value {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.detail-description {
  max-width: 100%;
  word-break: break-word;
  white-space: pre-wrap;
  color: #333;
}
</style>
